const { defineConfig, loadEnv } = require('vite')
const { createVue2Plugin } = require('./vite-vue2-plugin.js')
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 判断是否为开发环境
  const isDev = command === 'serve'

  // 根据环境变量确定代理配置
  const isMac = process.platform === 'darwin'
  const host = isMac ? '0.0.0.0' : '127.0.0.1'

  // 处理环境变量，过滤掉无效的标识符
  const processEnv = {}
  Object.keys(env).forEach(key => {
    // 只处理有效的 JavaScript 标识符，排除包含特殊字符的环境变量
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(key)) {
      processEnv[`process.env.${key}`] = JSON.stringify(env[key])
    }
  })

  // 手动添加一些常用的环境变量
  processEnv['process.env.NODE_ENV'] = JSON.stringify(mode)
  processEnv['process.env.ENV'] = JSON.stringify(env.ENV || mode)
  processEnv['process.env.ENV_NAME'] = JSON.stringify(env.ENV_NAME || '开发环境')
  processEnv['process.env.VITE_APP_TITLE'] = JSON.stringify(env.VITE_APP_TITLE || '活动中台后台管理系统')
  processEnv['process.env.VITE_APP_BASE_API'] = JSON.stringify(env.VITE_APP_BASE_API || '/api')
  processEnv['process.env.PERMISSION_HOST'] = JSON.stringify(env.PERMISSION_HOST || 'https://zhuiya-test.yy.com')

  // 代理配置
  let proxy = {}
  if (isDev) {
    const proxyLocal = false
    const target = proxyLocal ? `http://${host}:8080` : 'https://test-manager-hdzt.yy.com'

    proxy[env.VITE_APP_BASE_API || '/api'] = {
      target: target,
      changeOrigin: true,
      secure: false,
      rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_BASE_API || '/api'}`), '')
    }
  }

  return {
    plugins: [
      createVue2Plugin()
    ],

    resolve: {
      alias: {
        '@': resolve('src'),
        'vue$': 'vue/dist/vue.esm.js'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/css/_util.scss";`
        }
      }
    },

    server: {
      host: host,
      port: 4000,
      https: isDev,
      proxy: proxy,
      fs: {
        strict: false
      }
    },

    build: {
      outDir: 'output',
      assetsDir: 'static',
      sourcemap: false,
      rollupOptions: {
        external: ['jquery'],
        output: {
          globals: {
            jquery: 'jQuery'
          }
        }
      }
    },

    define: {
      // 将环境变量注入到代码中
      ...processEnv,
      __DEV__: isDev,
      __PROD__: !isDev,
      __TEST__: mode === 'staging'
    },

    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vuex',
        'axios',
        'element-ui',
        'js-cookie'
      ]
    }
  }
})
