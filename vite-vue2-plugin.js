// 简单的 Vue 2 插件，兼容 Node.js 12
const fs = require('fs')
const path = require('path')

function createVue2Plugin() {
  return {
    name: 'vue2-simple',
    transform(code, id) {
      if (id.endsWith('.vue')) {
        // 简单的 Vue 单文件组件处理
        const templateMatch = code.match(/<template[^>]*>([\s\S]*?)<\/template>/)
        const scriptMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/)
        const styleMatch = code.match(/<style[^>]*>([\s\S]*?)<\/style>/)
        
        let template = templateMatch ? templateMatch[1] : ''
        let script = scriptMatch ? scriptMatch[1] : 'export default {}'
        let style = styleMatch ? styleMatch[1] : ''
        
        // 处理模板
        template = template.replace(/\n/g, '\\n').replace(/"/g, '\\"')
        
        // 生成 JavaScript 代码
        const result = `
${script.replace('export default', 'const component = ')}
component.template = "${template}"
export default component
`
        
        return {
          code: result,
          map: null
        }
      }
    }
  }
}

module.exports = { createVue2Plugin }
