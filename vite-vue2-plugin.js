// 简单的 Vue 2 插件，兼容 Node.js 12
const fs = require('fs')
const path = require('path')

function createVue2Plugin() {
  return {
    name: 'vue2-simple',
    transform(code, id) {
      if (id.endsWith('.vue')) {
        // 简单的 Vue 单文件组件处理
        const templateMatch = code.match(/<template[^>]*>([\s\S]*?)<\/template>/)
        const scriptMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/)
        const styleMatch = code.match(/<style[^>]*>([\s\S]*?)<\/style>/)

        let template = templateMatch ? templateMatch[1].trim() : ''
        let script = scriptMatch ? scriptMatch[1] : 'export default {}'
        let style = styleMatch ? styleMatch[1] : ''

        // 处理模板，转义特殊字符
        template = template
          .replace(/\\/g, '\\\\')
          .replace(/"/g, '\\"')
          .replace(/\r?\n/g, '\\n')
          .replace(/\t/g, '\\t')

        // 处理脚本，确保正确导出
        let componentCode = script
        if (script.includes('export default')) {
          componentCode = script.replace(/export\s+default\s+/, 'const component = ')
        } else {
          componentCode = `const component = ${script}`
        }

        // 生成 JavaScript 代码
        const result = `
${componentCode}
if (component.template === undefined) {
  component.template = "${template}"
}
export default component
`

        return {
          code: result,
          map: null
        }
      }
    },
    handleHotUpdate({ file, server }) {
      if (file.endsWith('.vue')) {
        server.ws.send({
          type: 'full-reload'
        })
      }
    }
  }
}

module.exports = { createVue2Plugin }
