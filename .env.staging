# 测试环境配置
NODE_ENV=staging

# 页面标题
VITE_APP_TITLE=活动中台后台管理系统-测试环境

# 测试环境配置
ENV=staging

# 环境名称
ENV_NAME=测试环境

# 若依管理系统/测试环境
VITE_APP_BASE_API=/

# web应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH=/admin-static/

# 监控地址
VITE_APP_MONITRO_ADMIN=/admin/login

# xxl-job 控制台地址
VITE_APP_XXL_JOB_ADMIN=/xxl-job-admin

# zhuiya 权限管理host前缀
PERMISSION_HOST=https://zhuiya-test.yy.com

# CDN 路径
__CDN=/admin-static/

# Web 路径
__WEB=/admin-static/
