# 开发环境配置
NODE_ENV=development

# 页面标题
VITE_APP_TITLE=活动中台后台管理系统-开发环境

# 开发环境配置
ENV=development

# 环境名称
ENV_NAME=开发环境

# 若依管理系统/开发环境
VITE_APP_BASE_API=/api

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH=/

# 监控地址
VITE_APP_MONITRO_ADMIN=/admin/login

# xxl-job 控制台地址
VITE_APP_XXL_JOB_ADMIN=/xxl-job-admin

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES=true

# zhuiya 权限管理host前缀
PERMISSION_HOST=https://zhuiya-test.yy.com

# CDN 路径
__CDN=/

# Web 路径
__WEB=/
